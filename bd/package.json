{"name": "@clara/root", "version": "1.0.0", "description": "ClaraRx", "license": "UNLICENSED", "private": true, "type": "module", "packageManager": "pnpm@10.12.3", "keywords": [], "author": "<PERSON><PERSON>", "repository": {"url": "**************:clararx/bd.git", "type": "git"}, "scripts": {"lint": "pnpm eslint ."}, "prettier": "@clara/config/prettier/base.js", "workspaces": ["nes", "apps/*", "packages/*"]}