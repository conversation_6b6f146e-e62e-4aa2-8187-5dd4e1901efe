# Homebase Styling Guide

## Overview

The Homebase app uses a sophisticated multi-layered styling system that combines **Mantine UI**, **CSS Modules**, **SCSS**, and a **dynamic theme system**. This guide covers all styling approaches and best practices.

## 🎨 Styling Architecture

### Core Technologies
- **Mantine UI** - Primary component library with theme integration
- **CSS Modules** - Scoped, type-safe styling with auto-generated TypeScript types
- **SCSS** - Advanced preprocessing with mixins and functions
- **Zustand** - Dynamic theme state management
- **CSS Custom Properties** - Runtime theme switching

### Build System
- **Vite** - Fast development with HMR
- **vite-css-modules** - Automatic TypeScript type generation for CSS modules
- **Sass** - SCSS preprocessing with math and map functions

## 🏗️ Theme System

### Dynamic Theme Store

The app uses Zustand for theme state management:

```typescript
import { useThemeConfig, useThemeActions } from "@core/store/theme";

const MyComponent = () => {
    const themeConfig = useThemeConfig();
    const { setDarkMode, setCompactMode } = useThemeActions();
    
    return (
        <div style={{ color: themeConfig.color.primary.default }}>
            <button onClick={() => setDarkMode('dark')}>Dark Mode</button>
        </div>
    );
};
```

### Theme Variants

The theme system supports multiple variants:

- **Dark/Light Mode**: `auto` | `dark` | `light`
- **Compact Mode**: `standard` | `compact` 
- **Device Mode**: `desktop` | `tablet` | `mobile` (auto-detected)

### CSS Variables

All theme values are automatically converted to CSS custom properties:

```scss
.my-component {
    background-color: var(--color-primary-default);
    padding: var(--spacing-large);
    border-radius: var(--radius-medium);
    font-size: var(--font-size-large);
}
```

### Theme Configuration

Theme values are defined in TypeScript files:

```typescript
// src/styles/theme/var-base.ts
export const themeBase: ThemeConfig = {
    color: {
        primary: {
            default: "#837BB2",
            c25: "#F6F5F9",
            c950: "#433D66",
        },
    },
    spacing: {
        small: "8px",
        medium: "16px",
        large: "24px",
    },
};
```

## 🎯 Styling Approaches

### 1. Mantine Components (Recommended)

Use Mantine components for consistent UI patterns:

```typescript
import { Box, Button, Text } from "@mantine/core";

export function MyComponent() {
    return (
        <Box p="lg" m="md" bg="primary.5">
            <Text size="lg" fw={600}>Title</Text>
            <Button variant="filled" color="primary">
                Action
            </Button>
        </Box>
    );
}
```

**Benefits:**
- Automatic theme integration
- Accessibility built-in
- Consistent spacing/sizing
- TypeScript support

### 2. CSS Modules (For Custom Styling)

Create scoped, type-safe styles:

```scss
// MyComponent.module.scss
@use "@styles/theme";

.container {
    display: flex;
    gap: var(--spacing-medium);
    
    @include theme.dark() {
        background-color: var(--color-background-c900);
    }
    
    @include theme.mobile() {
        flex-direction: column;
    }
}

.title {
    font-size: var(--font-size-xlarge);
    color: var(--color-text-default);
    
    @include theme.compact() {
        font-size: var(--font-size-large);
    }
}
```

```typescript
// MyComponent.tsx
import styles from "./MyComponent.module.scss";

export function MyComponent() {
    return (
        <div className={styles.container}>
            <h2 className={styles.title}>My Title</h2>
        </div>
    );
}
```

**Benefits:**
- Scoped styles (no conflicts)
- TypeScript autocomplete
- Theme-aware mixins
- Hot reload support

### 3. Global SCSS (For App-Wide Styles)

Use for global styles and utilities:

```scss
// src/modules/app.scss
.logo {
    will-change: filter;
    height: 6em;
    transition: filter 300ms;
    
    &:hover {
        filter: drop-shadow(0 0 2em #646cffaa);
    }
}

.card {
    padding: var(--spacing-xxsmall);
    font-size: var(--font-size-large);
    color: var(--color-secondary-950);
}
```

### 4. Inline Styles (For Dynamic Values)

Use sparingly for dynamic styling:

```typescript
export function DynamicComponent({ color, size }) {
    const themeConfig = useThemeConfig();
    
    return (
        <div
            style={{
                backgroundColor: color || themeConfig.color.primary.default,
                fontSize: `${size}px`,
            }}
        >
            Dynamic content
        </div>
    );
}
```

## 🔧 SCSS Mixins & Utilities

### Theme Mixins

Use theme mixins for responsive and variant styling:

```scss
@use "@styles/theme";

.my-component {
    // Base styles
    padding: var(--spacing-medium);
    
    // Dark mode variant
    @include theme.dark() {
        background-color: var(--color-background-c800);
    }
    
    // Compact mode variant
    @include theme.compact() {
        padding: var(--spacing-small);
    }
    
    // Mobile responsive
    @include theme.mobile() {
        padding: var(--spacing-large);
        
        // Mobile + dark mode
        @include theme.dark() {
            border: 1px solid var(--color-border-c600);
        }
    }
}
```

### Available Mixins

- `@include theme.dark()` - Dark mode styles
- `@include theme.compact()` - Compact mode styles  
- `@include theme.mobile()` - Mobile device styles
- `@include theme.tablet()` - Tablet device styles
- `@include theme.landscape()` - Landscape orientation
- `@include theme.portrait()` - Portrait orientation

## 📱 Responsive Design

### Breakpoints

The theme system includes responsive breakpoints:

```typescript
breakpoints: {
    xs: "576px",   // Mobile
    sm: "768px",   // Large mobile
    md: "992px",   // Tablet
    lg: "1200px",  // Desktop
    xl: "1400px",  // Large desktop
}
```

### Device Detection

The app automatically detects device type and applies appropriate themes:

```typescript
// Automatic device detection based on window width
const deviceMode = getDeviceMode(width, themeConfig.breakpoints);
// Sets: 'mobile' | 'tablet' | 'desktop'
```

### Responsive Patterns

```scss
.responsive-grid {
    display: grid;
    gap: var(--spacing-medium);
    
    // Desktop: 3 columns
    grid-template-columns: repeat(3, 1fr);
    
    @include theme.tablet() {
        // Tablet: 2 columns
        grid-template-columns: repeat(2, 1fr);
    }
    
    @include theme.mobile() {
        // Mobile: 1 column
        grid-template-columns: 1fr;
        gap: var(--spacing-small);
    }
}
```

## 🎨 Color System

### Color Scales

Each color has a 13-step scale from c25 (lightest) to c950 (darkest):

```typescript
primary: {
    default: "#837BB2",  // Main brand color
    c25: "#F6F5F9",     // Very light
    c50: "#F6F5F9",     // Light
    c100: "#F6F5F9",    // Light
    // ... through c900, c950
}
```

### Usage Patterns

```scss
.status-card {
    // Use semantic color names
    &.success { border-color: var(--color-success-default); }
    &.warning { border-color: var(--color-warning-default); }
    &.error { border-color: var(--color-error-default); }
    
    // Use scale for variations
    background-color: var(--color-primary-c50);
    color: var(--color-primary-c900);
    
    @include theme.dark() {
        background-color: var(--color-primary-c900);
        color: var(--color-primary-c50);
    }
}
```

## 📏 Spacing & Sizing

### Spacing Scale

Consistent spacing using a scale system:

```typescript
spacing: {
    xxsmall: "4px",
    xsmall: "8px", 
    small: "12px",
    standard: "16px",
    medium: "20px",
    large: "24px",
    xlarge: "32px",
    // ... up to xxxxxxxxxlarge
}
```

### Usage

```scss
.component {
    // Use semantic spacing
    padding: var(--spacing-medium);
    margin-bottom: var(--spacing-large);
    gap: var(--spacing-small);
    
    // Compact mode adjustments
    @include theme.compact() {
        padding: var(--spacing-small);
        gap: var(--spacing-xsmall);
    }
}
```

## 🔤 Typography

### Font Sizes

```scss
.text-styles {
    // Use semantic font sizes
    font-size: var(--font-size-large);
    line-height: var(--line-height-large);
    
    // Responsive typography
    @include theme.mobile() {
        font-size: var(--font-size-xlarge);
    }
    
    @include theme.compact() {
        font-size: var(--font-size-medium);
    }
}
```

## 🛠️ Development Workflow

### CSS Module Setup

1. Create `.module.scss` files for component-specific styles
2. Import and use with TypeScript autocomplete:

```typescript
import styles from "./Component.module.scss";

export function Component() {
    return <div className={styles.container}>Content</div>;
}
```

### Theme Development

1. Modify theme files in `src/styles/theme/`
2. Use theme store hooks for dynamic values
3. Test across all theme variants (dark/light, compact, mobile)

### Hot Reload

- SCSS changes reload instantly
- Theme changes update in real-time
- CSS modules regenerate types automatically

## ✅ Best Practices

### Do's
- ✅ Use Mantine components for standard UI patterns
- ✅ Use CSS modules for custom component styling
- ✅ Use CSS variables for theme-aware values
- ✅ Use semantic color/spacing names
- ✅ Test across all theme variants
- ✅ Use theme mixins for responsive design

### Don'ts
- ❌ Don't use hardcoded colors/sizes
- ❌ Don't use global CSS for component-specific styles
- ❌ Don't ignore dark mode/compact mode variants
- ❌ Don't use `!important` unless absolutely necessary
- ❌ Don't mix styling approaches within a single component

### Performance
- Use CSS variables for runtime theme switching
- Leverage CSS modules for optimal bundle splitting
- Minimize inline styles for better caching

## 🔍 Debugging

### Theme Inspector

Use browser dev tools to inspect CSS variables:

```javascript
// Console: View all theme variables
Object.entries(getComputedStyle(document.documentElement))
    .filter(([key]) => key.startsWith('--'))
    .forEach(([key, value]) => console.log(key, value));
```

### CSS Module Types

TypeScript types are auto-generated in development:

```typescript
// Component.module.scss.d.ts (auto-generated)
declare const styles: {
    readonly container: string;
    readonly title: string;
};
export default styles;
```

---

This styling system provides maximum flexibility while maintaining consistency and type safety across the entire application.
