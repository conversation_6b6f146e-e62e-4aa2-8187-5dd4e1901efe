# Homebase - Modern React Application

A modern React application built with TypeScript, Mantine UI, and an extensible theming system. This is a complete rewrite of the legacy jsshared/jshomebase/tshomebase codebase using modern web technologies.

## 🚀 Quick Start

Make sure to follow the Wiki docs for setting up your local development environment first.

```bash
# Install dependencies
pnpm install -r; pnpm rebuild -r;

# Start development server
pnpm dev

# Run tests
pnpm test

# Build for production
pnpm build
```

## 📚 Documentation

Comprehensive documentation is available in this folder:

- **[OVERVIEW.md](./OVERVIEW.md)** - This project overview and quick reference
- **[SETUP.md](./SETUP.md)** - Complete setup guide, project structure, architecture, and development workflow
- **[STYLE.md](./STYLE.md)** - Comprehensive styling guide covering Mantine, CSS modules, SCSS, and theming
- **[TESTING.md](./TESTING.md)** - Playwright test suite documentation and testing strategies

## 🏗️ Architecture Overview

### Modern Stack
- **React** with TypeScript and strict type checking
- **Mantine UI** for consistent, accessible components
- **Zustand** for lightweight state management
- **SWR** for data fetching and caching
- **Vite** for fast development and optimized builds

### Styling System
- **Dynamic Theming** with dark/light mode and compact variants
- **CSS Modules** with TypeScript integration
- **SCSS** with advanced mixins and utilities
- **Responsive Design** with automatic device detection

### Key Features
- 🎨 **Advanced Theme System** - Dynamic switching between dark/light, compact, and device-specific themes
- 📱 **Responsive Design** - Mobile-first approach with automatic device detection
- 🔒 **Type Safety** - Strict TypeScript with auto-generated CSS module types
- 🧪 **Comprehensive Testing** - Playwright-based E2E testing with mocking support
- ⚡ **Performance** - Optimized builds with code splitting and tree shaking

## 🎯 Development

### Available Scripts

| Command | Description |
|---------|-------------|
| `pnpm dev` | Start development server with HMR |
| `pnpm build` | Build for production |
| `pnpm test` | Run Playwright test suite |
| `pnpm test:ui` | Run tests with interactive UI |
| `pnpm lint` | Run ESLint and Stylelint |
| `pnpm lint:fix` | Auto-fix linting issues |

### Environment Setup

The app supports multiple environments:
- **Vite Server**: `http://localhost:5173`
- **Local Dev Server**: `https://dev.local.clararx.com/homebase/`
- **Alternate URL for Local Dev**: `https://nes.clararx.orb.local/login`

## 🎨 Styling Quick Reference

### Theme Usage
```typescript
import { useThemeConfig } from "@core/store/theme";

const theme = useThemeConfig();
// Access: theme.color.primary.default, theme.spacing.large, etc.
```

### CSS Variables
```scss
.component {
    background: var(--color-primary-default);
    padding: var(--spacing-large);
    border-radius: var(--radius-medium);
}
```

### CSS Modules
```typescript
import styles from "./Component.module.scss";

export function Component() {
    return <div className={styles.container}>Content</div>;
}
```

### Mantine Components
```typescript
import { Box, Button } from "@mantine/core";

<Box p="lg" bg="primary.5">
    <Button variant="filled">Action</Button>
</Box>
```

## 🧪 Testing

The app includes a comprehensive Playwright test suite covering:
- ✅ Authentication and login flows
- ✅ DSL form loading and processing
- ✅ Navigation and UI interactions
- ✅ API integration with mocking support
- ✅ Responsive design validation
- ✅ Cross-browser compatibility

See [TESTING.md](./TESTING.md) for detailed testing documentation.

## 🔧 Configuration

### Environment Variables
```bash
# Test credentials (in configs/.env.[username])
PLAYWRIGHT_TEST_USERNAME=csr
PLAYWRIGHT_TEST_PASSWORD=test123
BASE_URL=https://dev.local.clararx.com
```

### Build Configuration
- **Vite** - Modern build tool with HMR
- **TypeScript** - Strict type checking
- **ESLint + Prettier** - Code quality and formatting
- **Stylelint** - CSS/SCSS linting

## 🚀 Deployment

The application is designed to run in containerized environments and supports:
- Static asset optimization
- Code splitting and lazy loading
- Progressive enhancement
- Graceful degradation for legacy browsers

## 🤝 Contributing

1. Follow the established patterns in the codebase
2. Use TypeScript for all new code
3. Follow the styling guide in [STYLE.md](./STYLE.md)
4. Add tests for new functionality
5. Ensure all linting passes

## 📖 Learn More

- [Mantine Documentation](https://mantine.dev/)
- [Zustand Documentation](https://zustand-demo.pmnd.rs/)
- [SWR Documentation](https://swr.vercel.app/)
- [Vite Documentation](https://vitejs.dev/)
- [Playwright Documentation](https://playwright.dev/)

---

**This application represents a modern, maintainable rewrite of legacy homebase functionality with improved performance, accessibility, and developer experience.**
