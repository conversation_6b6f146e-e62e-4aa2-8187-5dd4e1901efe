# Getting Started with Homebase

Homebase is a modern React application built with TypeScript, Mantine UI, and a sophisticated theming system. This is a complete rewrite of the legacy jsshared/jshomebase/tshomebase codebase using cutting-edge web technologies and best practices.

Make sure to follow the Wiki docs for setting up your local development environment first.

## Prerequisites
- Node.js (version 22 or higher)
- pnpm (recommended package manager)

## Installation
1. Clone the repository to your local machine.
2. Navigate to the `bd` root directory.
3. Run `pnpm install` to install the workspace dependencies.

## Available Scripts

In the project directory `apps/homebase`, you can run:

### `pnpm dev`
Runs the app in development mode with Hot Module Replacement (HMR).
- Automatic page reload when you make changes
- Fast refresh for React components
- Available at `http://localhost:5173`

### `pnpm build`
Builds the app for production to the `dist` folder.
- Optimized and minified bundles with content hashing
- Code splitting for optimal loading performance
- Tree-shaking to eliminate unused code

### `pnpm build:test`
Builds the app in testing mode for Playwright tests.

### `pnpm test`
Runs the comprehensive Playwright test suite.
- End-to-end testing with real browser automation
- Authentication, DSL integration, and UI interaction tests
- Automatic NES backend mocking when unavailable
- HTML reports with screenshots and videos on failure

### `pnpm test:ui`
Runs tests with Playwright's interactive UI for debugging and development.

### `pnpm test:debug`
Runs tests in debug mode with step-by-step execution and breakpoints.

### `pnpm lint`
Runs ESLint and Stylelint to check code quality and style consistency.

### `pnpm lint:fix`
Automatically fixes linting issues where possible.

## Project Structure

The `bd/apps/homebase` folder contains the following structure:

```
bd/apps/homebase/
├── README/                    # 📚 Documentation
│   ├── INTRO.md              # This getting started guide
│   ├── STYLE.md              # Comprehensive styling guide
│   └── TESTING.md            # Testing documentation
├── __tests__/                # Playwright test suite
├── __mocks__/                # Test mocks and helpers
├── src/
│   ├── assets/               # Static assets (images, icons)
│   ├── blocks/               # Reusable UI blocks
│   ├── components/           # Reusable React components
│   ├── core/                 # Core application logic
│   │   ├── providers/        # React context providers
│   │   ├── store/            # Zustand state stores
│   │   └── ui/               # UI utilities
│   ├── dsl/                  # Domain Specific Language components
│   │   ├── fields/           # DSL form fields
│   │   └── forms/            # DSL form structures
│   ├── hooks/                # Custom React hooks
│   ├── modules/              # Application modules/pages
│   ├── styles/               # Global styles and theme system
│   │   └── theme/            # Theme configuration files
│   ├── type/                 # TypeScript type definitions
│   ├── utils/                # Utility functions and helpers
│   └── main.tsx              # Application entry point
├── configs/                  # Environment configuration files
├── eslint.config.js          # ESLint configuration
├── index.html                # Main HTML template
├── package.json              # Dependencies and scripts
├── playwright.config.ts      # Playwright test configuration
├── stylelint.config.js       # Stylelint configuration
├── tsconfig.*.json           # TypeScript configurations
└── vite.config.ts            # Vite build configuration
```

### Key Directories

- **`src/core/`**: Application core with providers, stores, and utilities
- **`src/styles/theme/`**: Advanced theming system with TypeScript configuration
- **`src/dsl/`**: Domain Specific Language components for dynamic forms
- **`src/modules/`**: Main application modules and pages
- **`__tests__/`**: Comprehensive Playwright test suite
- **`README/`**: All documentation organized in one place

## Key Features

### Modern Architecture
- **React 19+** with TypeScript and strict type checking
- **Mantine UI** for consistent, accessible component library
- **Zustand** for lightweight, flexible state management
- **SWR** for REST API caching, revalidation and optimistic updates
- **Vite** with React Compiler for fast development and optimized builds

### Advanced Styling System
- **Dynamic Theming** with dark/light mode and compact variants
- **CSS Modules** with TypeScript integration and auto-generated types
- **SCSS** with advanced mixins and responsive utilities
- **CSS Variables** for runtime theme switching
- **Responsive Design** with automatic device detection

### Developer Experience
- **Type Safety** with strict TypeScript and advanced generics
- **Hot Module Replacement** for instant development feedback
- **Code Quality** with ESLint + Prettier + Stylelint integration
- **Comprehensive Testing** with Playwright E2E test suite
- **Performance Optimization** with code splitting and tree shaking

## Testing

This app uses **Playwright** for comprehensive end-to-end testing, providing coverage of:

- **Authentication**: Login flows and session management
- **DSL Integration**: Dynamic form loading and processing
- **UI Interactions**: Navigation, responsive design, and user workflows
- **API Integration**: Backend connectivity with intelligent mocking
- **Cross-Browser**: Chrome, Firefox, Safari, Edge, and mobile viewports

### Test Configuration

Tests use environment variables for credentials:
- `PLAYWRIGHT_TEST_USERNAME` (default: "csr")
- `PLAYWRIGHT_TEST_PASSWORD` (default: "test123")

These can be set in `configs/.env.[username]` files using dotenvx.

### Test Structure

- `__tests__/`: Test files organized by functionality (63 test cases)
- `__mocks__/`: Comprehensive API mocks and test helper utilities
- `playwright.config.ts`: Multi-browser configuration with dev server integration

The test suite automatically:
1. Starts the dev server on `http://localhost:5173`
2. Checks connectivity to NES backend at `https://dev.local.clararx.com`
3. Falls back to comprehensive API mocks when backend is unavailable
4. Validates build process and performance metrics
5. Provides detailed HTML reports with screenshots and videos

For detailed testing information, see [TESTING.md](./TESTING.md).

## Styling System

The app features a sophisticated multi-layered styling approach:

- **Mantine Components** for consistent UI patterns
- **CSS Modules** for scoped, type-safe component styling
- **Dynamic Theming** with runtime switching capabilities
- **SCSS Mixins** for responsive and variant styling
- **CSS Variables** for theme-aware styling

For comprehensive styling documentation, see [STYLE.md](./STYLE.md).

## 🏗️ Application Architecture

### Modern Technology Stack
```
Frontend Framework:    React + TypeScript
UI Components:         Mantine UI Library
State Management:      Zustand (lightweight)
Data Fetching:         SWR (caching + revalidation)
Build System:          Vite + React Compiler
Styling:              CSS Modules + SCSS + Dynamic Theming
Testing:              Playwright (E2E automation)
Code Quality:         ESLint + Prettier + Stylelint
```

### Key Architectural Decisions

#### 1. **Provider Composition Pattern**
```typescript
// Composable provider architecture
export const ComposedProviders = composeProviders(
    SWRProvider,    // Data fetching
    UIProvider      // Theme + Mantine
);
```

#### 2. **Dynamic Theme System**
- Runtime theme switching without rebuilds
- TypeScript-configured theme scales
- Automatic CSS variable generation
- Device-responsive theme variants

#### 3. **Type-Safe Styling**
- Auto-generated TypeScript types for CSS modules
- Theme-aware SCSS mixins
- Consistent spacing/color scales

#### 4. **Comprehensive Testing**
- Real browser automation with Playwright
- Intelligent API mocking
- Cross-browser compatibility validation

## 📚 Documentation Navigation

### Available Documentation
- **[OVERVIEW.md](./OVERVIEW.md)** - Project overview, quick start, and feature summary
- **[SETUP.md](./SETUP.md)** - This comprehensive setup and architecture guide
- **[STYLE.md](./STYLE.md)** - Complete styling guide with theming and CSS modules
- **[TESTING.md](./TESTING.md)** - Playwright test suite and testing strategies

### For New Developers
1. **Start Here**: [OVERVIEW.md](./OVERVIEW.md) - Project overview and quick start
2. **Deep Dive**: This guide - Setup, architecture, and development workflow
3. **Learn Styling**: [STYLE.md](./STYLE.md) - How to style components
4. **Run Tests**: [TESTING.md](./TESTING.md) - Understanding the test suite

### For Experienced Developers
- **Quick Reference**: [OVERVIEW.md](./OVERVIEW.md) - Architecture overview and examples
- **Styling Reference**: [STYLE.md](./STYLE.md) - Advanced theming and CSS modules
- **Testing Patterns**: [TESTING.md](./TESTING.md) - Test development and mocking
- **Architecture Details**: This guide - Project structure and key decisions

### For Designers/UX
- **Project Overview**: [OVERVIEW.md](./OVERVIEW.md) - Features and capabilities
- **Theme System**: [STYLE.md](./STYLE.md) - Color scales, spacing, typography
- **Responsive Design**: [STYLE.md](./STYLE.md) - Breakpoints and device variants
- **Component Library**: Mantine UI documentation (external)

## 🔧 Development Workflow

### Daily Development
```bash
# Start development server
pnpm dev

# Run linting
pnpm lint

# Run tests
pnpm test:ui
```

### Styling Development
```bash
# CSS modules auto-generate types
# SCSS changes reload instantly
# Theme changes update in real-time
```

### Testing Development
```bash
# Interactive test development
pnpm test:ui

# Debug specific tests
pnpm test:debug
```

## Development Tools

- **VS Code** with recommended extensions for TypeScript, ESLint, and Prettier
- **Hot Module Replacement** for instant feedback during development
- **React Compiler** for optimized component rendering
- **Playwright Test Runner** with UI mode for interactive debugging
- **TypeScript** with strict checking and auto-generated CSS module types

## 📖 External Resources

### Core Technologies
- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Mantine UI Components](https://mantine.dev/)
- [Zustand State Management](https://zustand-demo.pmnd.rs/)
- [SWR Data Fetching](https://swr.vercel.app/)
- [Vite Build Tool](https://vitejs.dev/)
- [Playwright Testing](https://playwright.dev/)

### Styling Resources
- [CSS Modules](https://github.com/css-modules/css-modules)
- [Sass/SCSS](https://sass-lang.com/)
- [CSS Custom Properties](https://developer.mozilla.org/en-US/docs/Web/CSS/--*)

## 🤝 Contributing

### Code Standards
1. **TypeScript**: Use strict typing throughout
2. **Styling**: Follow the patterns in [STYLE.md](./STYLE.md)
3. **Testing**: Add tests for new functionality
4. **Documentation**: Update relevant documentation

### Development Process
1. Read the appropriate documentation section
2. Follow established patterns and conventions
3. Test changes across theme variants
4. Ensure all linting passes
5. Add/update tests as needed