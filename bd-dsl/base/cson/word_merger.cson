fields:

	name:
		model:
			required: true
		view:
			label: 'Name'

	code:
		model:
			required: true
		view:
			# offscreen: false
			# readonly: false
			label: 'Code'

	template_json:
		model:
			required: false
			type: 'json'
		view:
			offscreen: true
			readonly: true
			label: 'Template Definations'

	allow_sync:
		model:
			source: ['Yes', 'No']
			default: 'No'
		view:
			control: 'radio'
			label: 'Can Sync Record'
	active:
		model:
			default: 'Yes'
			source: ['Yes', 'No']
		view:
			control: 'radio'
			label: 'Active'

model:
	access:
		create:     []
		create_all: ['admin']
		delete:     ['admin']
		read:       ['admin']
		read_all:   ['admin']
		request:    []
		update:     []
		update_all: ['admin']
		write:      ['admin']
	bundle: ['setup']
	name: '{name}'
	sync_mode: 'mixed'
	indexes:
		unique:[
			['code']
		]
	sections:
		'Main':
			fields: ['name', 'code']
		'Parameters':
			fields: ['allow_sync', 'active']

view:
	comment: 'Word Merger'
	grid:
		fields: ['name', 'code', 'allow_sync', 'active']
		sort: ['name', 'code']
	label: 'Word Merger'
	open: 'read'
